<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置订单支付状态测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #007bff;
        }

        .test-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        .btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        .btn.success {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        }

        .btn.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .result.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .result.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .quick-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-unpaid { background: #fff3cd; color: #856404; }
        .status-paid { background: #d4edda; color: #155724; }
        .status-delivered { background: #cce5ff; color: #004085; }
        .status-cancelled { background: #f8d7da; color: #721c24; }

        .order-history {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
        }

        .order-item {
            padding: 8px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .order-item:hover {
            background-color: #f8f9fa;
        }

        .order-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 订单支付状态设置工具</h1>
            <p>用于测试和管理订单支付状态的工具</p>
        </div>

        <div class="content">
            <!-- 设置支付状态 -->
            <div class="test-section">
                <h2>📝 设置订单支付状态</h2>
                
                <div class="form-group">
                    <label for="orderId">订单ID:</label>
                    <input type="text" id="orderId" placeholder="输入订单ID，例如：ORDER123456789">
                </div>

                <div class="form-group">
                    <label for="paymentStatus">支付状态:</label>
                    <select id="paymentStatus">
                        <option value="unpaid">未支付 (unpaid)</option>
                        <option value="paid">已支付 (paid)</option>
                        <option value="delivered">已发货 (delivered)</option>
                        <option value="cancelled">已取消 (cancelled)</option>
                    </select>
                </div>

                <div class="quick-actions">
                    <button class="btn" onclick="setPaymentStatus()">设置支付状态</button>
                    <button class="btn secondary" onclick="checkOrderStatus()">查询订单状态</button>
                    <button class="btn success" onclick="quickSetPaid()">快速设为已支付</button>
                    <button class="btn danger" onclick="clearResult()">清空结果</button>
                </div>

                <div id="result" class="result" style="display: none;"></div>
            </div>

            <!-- 批量操作 -->
            <div class="test-section">
                <h2>⚡ 批量操作</h2>
                
                <div class="form-group">
                    <label for="batchOrderIds">批量订单ID (每行一个):</label>
                    <textarea id="batchOrderIds" rows="4" style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; resize: vertical;" placeholder="ORDER123456789&#10;ORDER987654321&#10;ORDER555666777"></textarea>
                </div>

                <div class="form-group">
                    <label for="batchStatus">批量设置状态:</label>
                    <select id="batchStatus">
                        <option value="paid">已支付 (paid)</option>
                        <option value="delivered">已发货 (delivered)</option>
                        <option value="cancelled">已取消 (cancelled)</option>
                        <option value="unpaid">未支付 (unpaid)</option>
                    </select>
                </div>

                <div class="quick-actions">
                    <button class="btn" onclick="batchSetStatus()">批量设置状态</button>
                    <button class="btn secondary" onclick="batchCheckStatus()">批量查询状态</button>
                </div>

                <div id="batchResult" class="result" style="display: none;"></div>
            </div>

            <!-- 最近操作记录 -->
            <div class="test-section">
                <h2>📋 最近操作记录</h2>
                <div id="orderHistory" class="order-history">
                    <p style="text-align: center; color: #666; padding: 20px;">暂无操作记录</p>
                </div>
                <div class="quick-actions">
                    <button class="btn secondary" onclick="clearHistory()">清空记录</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 操作历史记录
        let operationHistory = JSON.parse(localStorage.getItem('orderOperationHistory') || '[]');

        // 页面加载时显示历史记录
        window.onload = function() {
            displayHistory();
        };

        // 设置支付状态
        async function setPaymentStatus() {
            const orderId = document.getElementById('orderId').value.trim();
            const paymentStatus = document.getElementById('paymentStatus').value;
            const resultDiv = document.getElementById('result');

            if (!orderId) {
                showResult('请输入订单ID', 'error');
                return;
            }

            showResult('正在设置支付状态...', '');

            try {
                const response = await fetch(`https://cloudshop.qnm6.top/set_order_payment_status.php?order_id=${encodeURIComponent(orderId)}&payment_status=${paymentStatus}`);
                const data = await response.json();

                const resultText = JSON.stringify(data, null, 2);
                const resultClass = data.status === 'success' ? 'success' : 'error';
                
                showResult(resultText, resultClass);

                // 记录操作历史
                addToHistory({
                    orderId: orderId,
                    action: '设置状态',
                    status: paymentStatus,
                    result: data.status,
                    timestamp: new Date().toLocaleString()
                });

            } catch (error) {
                showResult('网络错误: ' + error.message, 'error');
            }
        }

        // 查询订单状态
        async function checkOrderStatus() {
            const orderId = document.getElementById('orderId').value.trim();
            
            if (!orderId) {
                showResult('请输入订单ID', 'error');
                return;
            }

            showResult('正在查询订单状态...', '');

            try {
                const response = await fetch(`https://cloudshop.qnm6.top/check_payment_status.php?order_id=${encodeURIComponent(orderId)}`);
                const data = await response.json();

                const resultText = JSON.stringify(data, null, 2);
                const resultClass = data.status === 'success' ? 'success' : 'error';
                
                showResult(resultText, resultClass);

                // 记录操作历史
                addToHistory({
                    orderId: orderId,
                    action: '查询状态',
                    status: data.data?.order_status || '未知',
                    result: data.status,
                    timestamp: new Date().toLocaleString()
                });

            } catch (error) {
                showResult('网络错误: ' + error.message, 'error');
            }
        }

        // 快速设为已支付
        function quickSetPaid() {
            document.getElementById('paymentStatus').value = 'paid';
            setPaymentStatus();
        }

        // 批量设置状态
        async function batchSetStatus() {
            const orderIds = document.getElementById('batchOrderIds').value.trim().split('\n').filter(id => id.trim());
            const status = document.getElementById('batchStatus').value;
            const resultDiv = document.getElementById('batchResult');

            if (orderIds.length === 0) {
                showBatchResult('请输入至少一个订单ID', 'error');
                return;
            }

            showBatchResult(`正在批量设置 ${orderIds.length} 个订单状态为 ${status}...`, '');

            const results = [];
            for (let i = 0; i < orderIds.length; i++) {
                const orderId = orderIds[i].trim();
                if (!orderId) continue;

                try {
                    const response = await fetch(`https://cloudshop.qnm6.top/set_order_payment_status.php?order_id=${encodeURIComponent(orderId)}&payment_status=${status}`);
                    const data = await response.json();
                    
                    results.push({
                        orderId: orderId,
                        status: data.status,
                        message: data.message
                    });

                    // 记录操作历史
                    addToHistory({
                        orderId: orderId,
                        action: '批量设置',
                        status: status,
                        result: data.status,
                        timestamp: new Date().toLocaleString()
                    });

                } catch (error) {
                    results.push({
                        orderId: orderId,
                        status: 'error',
                        message: error.message
                    });
                }
            }

            const resultText = results.map(r => 
                `订单: ${r.orderId}\n状态: ${r.status}\n消息: ${r.message}\n${'='.repeat(50)}`
            ).join('\n');

            showBatchResult(resultText, 'success');
        }

        // 批量查询状态
        async function batchCheckStatus() {
            const orderIds = document.getElementById('batchOrderIds').value.trim().split('\n').filter(id => id.trim());
            
            if (orderIds.length === 0) {
                showBatchResult('请输入至少一个订单ID', 'error');
                return;
            }

            showBatchResult(`正在批量查询 ${orderIds.length} 个订单状态...`, '');

            const results = [];
            for (let i = 0; i < orderIds.length; i++) {
                const orderId = orderIds[i].trim();
                if (!orderId) continue;

                try {
                    const response = await fetch(`https://cloudshop.qnm6.top/check_payment_status.php?order_id=${encodeURIComponent(orderId)}`);
                    const data = await response.json();
                    
                    results.push({
                        orderId: orderId,
                        status: data.status,
                        orderStatus: data.data?.order_status || '未知',
                        message: data.message
                    });

                } catch (error) {
                    results.push({
                        orderId: orderId,
                        status: 'error',
                        orderStatus: '错误',
                        message: error.message
                    });
                }
            }

            const resultText = results.map(r => 
                `订单: ${r.orderId}\n查询状态: ${r.status}\n订单状态: ${r.orderStatus}\n消息: ${r.message}\n${'='.repeat(50)}`
            ).join('\n');

            showBatchResult(resultText, 'success');
        }

        // 显示结果
        function showResult(text, className) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = text;
            resultDiv.className = 'result ' + className;
            resultDiv.style.display = 'block';
        }

        // 显示批量结果
        function showBatchResult(text, className) {
            const resultDiv = document.getElementById('batchResult');
            resultDiv.textContent = text;
            resultDiv.className = 'result ' + className;
            resultDiv.style.display = 'block';
        }

        // 清空结果
        function clearResult() {
            document.getElementById('result').style.display = 'none';
            document.getElementById('batchResult').style.display = 'none';
        }

        // 添加到历史记录
        function addToHistory(operation) {
            operationHistory.unshift(operation);
            if (operationHistory.length > 50) {
                operationHistory = operationHistory.slice(0, 50);
            }
            localStorage.setItem('orderOperationHistory', JSON.stringify(operationHistory));
            displayHistory();
        }

        // 显示历史记录
        function displayHistory() {
            const historyDiv = document.getElementById('orderHistory');
            
            if (operationHistory.length === 0) {
                historyDiv.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">暂无操作记录</p>';
                return;
            }

            const historyHtml = operationHistory.map(op => `
                <div class="order-item" onclick="fillOrderId('${op.orderId}')">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <strong>${op.orderId}</strong>
                            <span class="status-badge status-${op.status}">${op.status}</span>
                        </div>
                        <div style="font-size: 12px; color: #666;">
                            ${op.action} | ${op.result} | ${op.timestamp}
                        </div>
                    </div>
                </div>
            `).join('');

            historyDiv.innerHTML = historyHtml;
        }

        // 填充订单ID
        function fillOrderId(orderId) {
            document.getElementById('orderId').value = orderId;
        }

        // 清空历史记录
        function clearHistory() {
            if (confirm('确定要清空所有操作记录吗？')) {
                operationHistory = [];
                localStorage.removeItem('orderOperationHistory');
                displayHistory();
            }
        }
    </script>
</body>
</html>
