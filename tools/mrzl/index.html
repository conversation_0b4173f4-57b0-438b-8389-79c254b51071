<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每日自律-猎户助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: url('https://www.loliapi.com/acg/pe/') center/cover no-repeat fixed;
            min-height: 100vh;
            padding: 16px;
            margin: 0;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.85);
            z-index: -1;
        }

        .top-bar {
            background: url('https://www.loliapi.com/acg/pc/') center/cover no-repeat;
            border: 3px solid #333;
            border-radius: 8px;
            padding: 20px 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 16px;
            box-shadow: 4px 4px 0px #333;
            min-height: 120px;
            position: relative;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            backdrop-filter: blur(2px);
        }

        .pixel-icons {
            display: flex;
            gap: 12px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .pixel-icon {
            width: 24px;
            height: 24px;
            background: #ff6b35;
            border: 2px solid #333;
            position: relative;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .pixel-icon.orange {
            background: #ff6b35;
        }

        .pixel-icon.gray {
            background: #999;
        }

        .pixel-icon.green {
            background: #4CAF50;
        }

        .pixel-icon.blue {
            background: #2196F3;
            position: relative;
            z-index: 2;
        }

        .pixel-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
        }

        .header-card {
            background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(5px);
        }

        .app-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 16px 0;
            text-shadow: 2px 2px 0px rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-title::before {
            content: '💪';
            font-size: 16px;
        }

        .back-btn {
            background: rgba(0,0,0,0.3);
            color: white;
            border: 3px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.1s ease;
            text-shadow: 1px 1px 0px rgba(0,0,0,0.5);
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.5);
            transform: translate(1px, 1px);
            box-shadow: 1px 1px 0px rgba(0,0,0,0.3);
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
        }

        .zilv-container {
            margin: 20px 0;
            border: 3px solid #333;
            border-radius: 8px;
            background: rgba(249, 249, 249, 0.95);
            min-height: 200px;
            position: relative;
            overflow: hidden;
            padding: 20px;
        }

        .zilv-loading {
            font-size: 16px;
            font-weight: bold;
            color: #666;
            animation: pulse 1.5s infinite;
            text-align: center;
            padding: 40px;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .video-content {
            display: none;
            width: 100%;
            text-align: center;
        }

        .video-content.show {
            display: block;
        }

        .video-player {
            width: 100%;
            margin-bottom: 16px;
        }

        #videoContainer {
            width: 100%;
            max-height: 300px;
            border: 3px solid #333;
            border-radius: 8px;
            background: #000;
            overflow: hidden;
        }

        .video-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 12px;
            text-align: center;
            padding: 12px;
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.1);
        }

        .zilv-btn {
            background: #9C27B0;
            color: white;
            border: 3px solid #333;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            cursor: pointer;
            transition: all 0.1s ease;
            margin: 6px 4px;
            box-shadow: 3px 3px 0px #333;
            width: 100%;
        }

        .zilv-btn:hover {
            transform: translate(1px, 1px);
            box-shadow: 2px 2px 0px #333;
        }

        .zilv-btn:active {
            transform: translate(2px, 2px);
            box-shadow: 1px 1px 0px #333;
        }

        .random-btn {
            background: #4CAF50;
        }

        .error {
            color: #f44336;
            margin-top: 10px;
            font-size: 12px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            text-align: center;
        }

        @media (max-width: 480px) {
            .top-bar {
                margin: 0 8px 12px 8px;
                padding: 16px 12px;
                min-height: 100px;
            }
            
            .header-card {
                margin: 0 8px 12px 8px;
                padding: 16px;
            }
            
            .container {
                margin: 0 8px;
                padding: 16px;
            }
            
            .app-title {
                font-size: 16px;
            }
            
            h1 {
                font-size: 18px;
            }
            
            .zilv-container {
                min-height: 180px;
                padding: 16px;
            }

            #videoContainer {
                max-height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="top-bar">
        <div class="pixel-icons">
            <div class="pixel-icon orange"></div>
            <div class="pixel-icon gray"></div>
            <div class="pixel-icon green"></div>
        </div>
        <div class="pixel-icon blue"></div>
    </div>
    
    <div class="header-card">
        <h2 class="app-title">每日自律-猎户助手</h2>
        <button class="back-btn" onclick="goBack()">返回首页</button>
    </div>
    
    <div class="container">
        <h1>💪 每日自律</h1>

        <div class="zilv-container" id="zilvContainer">
            <div class="zilv-loading" id="zilvLoading">正在加载视频...</div>

            <div class="video-content" id="videoContent">
                <div class="video-title" id="videoTitle">📺 正在播放</div>
                <div class="video-player">
                    <div id="videoContainer"></div>
                </div>
            </div>
        </div>

        <button class="zilv-btn" onclick="loadRandomVideo()">🔄 换一个视频</button>
        <button class="zilv-btn random-btn" onclick="playRandomVideo()">🎲 随机播放</button>

        <div class="error" id="errorMsg"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script>
        let isLoading = false;
        let videoData = [];
        let currentVideo = null;
        let hls = null;

        // 加载视频数据
        async function loadVideoData() {
            try {
                const response = await fetch('./zilv.json');

                if (!response.ok) {
                    throw new Error('网络请求失败');
                }

                const data = await response.json();

                if (Array.isArray(data) && data.length > 0) {
                    videoData = data;
                    return true;
                } else {
                    throw new Error('视频列表为空或格式错误');
                }

            } catch (error) {
                console.error('加载视频数据失败:', error);
                document.getElementById('errorMsg').textContent = '加载失败: ' + error.message;
                return false;
            }
        }

        // 获取随机视频
        function getRandomVideo() {
            if (videoData.length === 0) return null;
            const randomIndex = Math.floor(Math.random() * videoData.length);
            return videoData[randomIndex];
        }

        // 播放视频
        function playVideo(video) {
            if (!video || !video.m3u8) {
                document.getElementById('errorMsg').textContent = '视频链接无效';
                return;
            }

            currentVideo = video;
            const videoTitle = document.getElementById('videoTitle');
            const videoContainer = document.getElementById('videoContainer');

            // 设置标题
            videoTitle.textContent = `📺 ${video.name || '未命名视频'}`;

            // 清理之前的播放器
            if (hls) {
                hls.destroy();
                hls = null;
            }

            // 创建video元素
            videoContainer.innerHTML = '<video id="videoElement" controls style="width: 100%; height: 100%;"></video>';
            const videoElement = document.getElementById('videoElement');

            // 检查是否支持HLS
            if (Hls.isSupported()) {
                hls = new Hls({
                    debug: false,
                    enableWorker: true,
                    lowLatencyMode: true,
                });

                hls.loadSource(video.m3u8);
                hls.attachMedia(videoElement);

                hls.on(Hls.Events.MANIFEST_PARSED, function() {
                    console.log('HLS manifest parsed, ready to play');
                });

                hls.on(Hls.Events.ERROR, function(event, data) {
                    console.error('HLS error:', data);
                    if (data.fatal) {
                        document.getElementById('errorMsg').textContent = 'HLS播放错误: ' + data.type;
                    }
                });

            } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
                // Safari原生支持
                videoElement.src = video.m3u8;
            } else {
                document.getElementById('errorMsg').textContent = '浏览器不支持HLS播放';
            }
        }

        // 加载随机视频
        async function loadRandomVideo() {
            if (isLoading) return;

            isLoading = true;
            const loading = document.getElementById('zilvLoading');
            const videoContent = document.getElementById('videoContent');
            const errorMsg = document.getElementById('errorMsg');

            // 显示加载状态
            loading.style.display = 'block';
            videoContent.classList.remove('show');
            errorMsg.textContent = '';

            // 如果没有数据，先加载
            if (videoData.length === 0) {
                const success = await loadVideoData();
                if (!success) {
                    loading.style.display = 'none';
                    isLoading = false;
                    return;
                }
            }

            // 获取随机视频
            const randomVideo = getRandomVideo();
            if (randomVideo) {
                playVideo(randomVideo);
                loading.style.display = 'none';
                videoContent.classList.add('show');
            } else {
                loading.style.display = 'none';
                errorMsg.textContent = '没有可播放的视频';
            }

            isLoading = false;
        }

        // 随机播放（快捷方式）
        function playRandomVideo() {
            loadRandomVideo();
        }

        // 返回根目录
        function goBack() {
            window.location.href = '/';
        }

        // 页面加载完成后自动播放随机视频
        window.addEventListener('load', function() {
            loadRandomVideo();
        });
    </script>
</body>
</html>
