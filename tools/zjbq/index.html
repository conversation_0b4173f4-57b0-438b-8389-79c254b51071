<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>证件补齐-猎户助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: url('https://www.loliapi.com/acg/pe/') center/cover no-repeat fixed;
            min-height: 100vh;
            padding: 16px;
            margin: 0;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.85);
            z-index: -1;
        }

        .top-bar {
            background: url('https://www.loliapi.com/acg/pc/') center/cover no-repeat;
            border: 3px solid #333;
            border-radius: 8px;
            padding: 20px 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 16px;
            box-shadow: 4px 4px 0px #333;
            min-height: 120px;
            position: relative;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            backdrop-filter: blur(2px);
        }

        .pixel-icons {
            display: flex;
            gap: 12px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .pixel-icon {
            width: 24px;
            height: 24px;
            background: #ff6b35;
            border: 2px solid #333;
            position: relative;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .pixel-icon.orange {
            background: #ff6b35;
        }

        .pixel-icon.gray {
            background: #999;
        }

        .pixel-icon.green {
            background: #4CAF50;
        }

        .pixel-icon.blue {
            background: #2196F3;
            position: relative;
            z-index: 2;
        }

        .pixel-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
        }

        .header-card {
            background: linear-gradient(135deg, #8BC34A 0%, #689F38 100%);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(5px);
        }

        .app-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 16px 0;
            text-shadow: 2px 2px 0px rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-title::before {
            content: '🆔';
            font-size: 16px;
        }

        .back-btn {
            background: rgba(0,0,0,0.3);
            color: white;
            border: 3px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.1s ease;
            text-shadow: 1px 1px 0px rgba(0,0,0,0.5);
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.5);
            transform: translate(1px, 1px);
            box-shadow: 1px 1px 0px rgba(0,0,0,0.3);
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
        }

        .input-group {
            margin-bottom: 16px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 6px;
            color: #333;
            font-weight: bold;
            font-size: 14px;
        }

        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 3px solid #333;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: all 0.1s ease;
            outline: none;
            background: #f9f9f9;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
        }

        input[type="text"]:focus, input[type="password"]:focus {
            background: white;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.2);
        }

        button {
            background: #8BC34A;
            color: white;
            border: 3px solid #333;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            cursor: pointer;
            transition: all 0.1s ease;
            margin: 6px 4px;
            box-shadow: 3px 3px 0px #333;
        }

        button:hover {
            transform: translate(1px, 1px);
            box-shadow: 2px 2px 0px #333;
        }

        button:active {
            transform: translate(2px, 2px);
            box-shadow: 1px 1px 0px #333;
        }

        .complete-btn {
            width: 100%;
        }

        .result-container {
            margin-top: 20px;
            padding: 20px;
            background: rgba(249, 249, 249, 0.95);
            border: 3px solid #333;
            border-radius: 8px;
            display: none;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
            backdrop-filter: blur(5px);
        }

        .result-container.show {
            display: block;
        }

        .result-content {
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
            font-size: 13px;
            line-height: 1.8;
            white-space: pre-line;
            word-break: break-word;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.1);
            font-family: 'Courier New', monospace;
        }

        .result-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            font-size: 13px;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.1);
        }

        .result-item-header {
            font-weight: bold;
            color: #8BC34A;
            margin-bottom: 8px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 4px;
        }

        .result-field {
            margin: 4px 0;
            display: flex;
            justify-content: space-between;
        }

        .result-label {
            font-weight: bold;
            color: #666;
        }

        .result-value {
            color: #333;
            font-family: monospace;
        }

        .loading {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            color: #666;
            animation: pulse 1.5s infinite;
            padding: 40px;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .error {
            color: #f44336;
            margin-top: 10px;
            font-size: 12px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            text-align: center;
        }

        .warning {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #856404;
            text-align: center;
        }

        .success-msg {
            color: #4CAF50;
            font-weight: bold;
            text-align: center;
            margin-bottom: 16px;
        }

        .feature-note {
            background: #f1f8e9;
            border: 2px solid #8BC34A;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #689f38;
            text-align: center;
        }

        .example-box {
            background: #f5f5f5;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #666;
        }

        .example-box h4 {
            color: #8BC34A;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .example-box code {
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        @media (max-width: 480px) {
            .top-bar {
                margin: 0 8px 12px 8px;
                padding: 16px 12px;
                min-height: 100px;
            }
            
            .header-card {
                margin: 0 8px 12px 8px;
                padding: 16px;
            }
            
            .container {
                margin: 0 8px;
                padding: 16px;
            }
            
            .app-title {
                font-size: 16px;
            }
            
            h1 {
                font-size: 18px;
            }
            
            button {
                width: 100%;
                margin: 4px 0;
            }

            .result-field {
                flex-direction: column;
                gap: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="top-bar">
        <div class="pixel-icons">
            <div class="pixel-icon orange"></div>
            <div class="pixel-icon gray"></div>
            <div class="pixel-icon green"></div>
        </div>
        <div class="pixel-icon blue"></div>
    </div>
    
    <div class="header-card">
        <h2 class="app-title">证件补齐-猎户助手</h2>
        <button class="back-btn" onclick="goBack()">返回首页</button>
    </div>
    
    <div class="container">
        <h1>🆔 证件补齐</h1>
        
        <div class="feature-note">
            🔧 身份证号补全 - 根据姓名和部分身份证号推测完整号码
        </div>
        
        <div class="example-box">
            <h4>📝 使用示例：</h4>
            <p><strong>姓名：</strong>张三</p>
            <p><strong>模糊身份证示例：</strong></p>
            <ul style="margin-left: 16px; line-height: 1.6;">
                <li><code>11010119900101xxxx</code> - 后4位未知</li>
                <li><code>440301197110292xxx</code> - 后3位未知</li>
                <li><code>44030119711029xxxx</code> - 后4位未知</li>
                <li><code>110101199001xxxxxx</code> - 后6位未知</li>
            </ul>
            <p>用 <code>x</code> 代替未知数字，系统会尝试补全完整身份证号</p>
        </div>
        
        <div class="warning">
            ⚠️ 请确保使用用途合法，仅用于合理的身份验证需求
        </div>
        
        <div class="input-group">
            <label for="nameInput">姓名：</label>
            <input type="text" id="nameInput" placeholder="请输入真实姓名">
            <div class="error" id="nameError"></div>
        </div>

        <div class="input-group">
            <label for="idcardInput">模糊身份证号：</label>
            <input type="text" id="idcardInput" placeholder="如：11010119900101xxxx (用x代替未知位)" maxlength="18">
            <div class="error" id="idcardError"></div>
        </div>

        <div class="input-group">
            <label for="tokenInput">卡密(Token)：</label>
            <input type="password" id="tokenInput" placeholder="请输入您的Token">
            <div class="error" id="tokenError"></div>
        </div>

        <button class="complete-btn" onclick="completeIdCard()">🔍 开始补齐</button>

        <div class="result-container" id="resultContainer">
            <div id="loadingMsg" class="loading" style="display: none;">正在补齐身份证号...</div>
            <div id="resultContent"></div>
        </div>
        
        <div class="error" id="errorMsg"></div>
    </div>

    <script>
        let isCompleting = false;

        // 验证姓名格式
        function validateName(name) {
            const nameRegex = /^[\u4e00-\u9fa5]{2,10}$/;
            return nameRegex.test(name);
        }

        // 验证模糊身份证格式
        function validateFuzzyIdCard(idcard) {
            // 基本长度检查
            if (idcard.length !== 18) {
                return false;
            }

            // 必须包含x字符
            if (!idcard.toLowerCase().includes('x')) {
                return false;
            }

            // 检查前6位是否为数字（地区码）
            const areaCode = idcard.substring(0, 6);
            if (!/^[1-9]\d{5}$/.test(areaCode)) {
                return false;
            }

            // 检查年份部分（第7-10位）
            const yearPart = idcard.substring(6, 10);
            if (!/^(19|20)\d{2}$/.test(yearPart) && !/^(19|20)[x\d]{2}$/i.test(yearPart)) {
                return false;
            }

            // 其余位可以是数字或x
            const remaining = idcard.substring(10);
            if (!/^[0-9x]{8}$/i.test(remaining)) {
                return false;
            }

            return true;
        }

        // 解析API返回的数据
        function parseResults(msg) {
            const results = [];
            const entries = msg.split('\n\n').filter(entry => entry.trim());
            
            entries.forEach((entry, index) => {
                const lines = entry.split('\n');
                let name = '';
                let idcard = '';
                
                lines.forEach(line => {
                    if (line.startsWith('Name:')) {
                        name = line.replace('Name:', '').trim();
                    } else if (line.startsWith('IdCard:')) {
                        idcard = line.replace('IdCard:', '').trim();
                    }
                });
                
                if (name && idcard) {
                    results.push({ name, idcard, index: index + 1 });
                }
            });
            
            return results;
        }

        // 补齐身份证号
        async function completeIdCard() {
            if (isCompleting) return;
            
            const nameInput = document.getElementById('nameInput');
            const idcardInput = document.getElementById('idcardInput');
            const tokenInput = document.getElementById('tokenInput');
            const nameError = document.getElementById('nameError');
            const idcardError = document.getElementById('idcardError');
            const tokenError = document.getElementById('tokenError');
            const errorMsg = document.getElementById('errorMsg');
            const resultContainer = document.getElementById('resultContainer');
            const loadingMsg = document.getElementById('loadingMsg');
            const resultContent = document.getElementById('resultContent');
            
            // 清除之前的错误信息
            nameError.textContent = '';
            idcardError.textContent = '';
            tokenError.textContent = '';
            errorMsg.textContent = '';
            
            const name = nameInput.value.trim();
            const idcard = idcardInput.value.trim();
            const token = tokenInput.value.trim();
            
            // 验证输入
            let hasError = false;
            
            if (!name) {
                nameError.textContent = '请输入姓名';
                hasError = true;
            } else if (!validateName(name)) {
                nameError.textContent = '请输入正确的中文姓名(2-10个字符)';
                hasError = true;
            }
            
            if (!idcard) {
                idcardError.textContent = '请输入模糊身份证号';
                hasError = true;
            } else if (!validateFuzzyIdCard(idcard)) {
                idcardError.textContent = '请输入正确的模糊身份证号(包含x代替未知位)';
                hasError = true;
            }
            
            if (!token) {
                tokenError.textContent = '请输入Token';
                hasError = true;
            }
            
            if (hasError) {
                return;
            }
            
            isCompleting = true;
            
            // 显示加载状态
            loadingMsg.style.display = 'block';
            resultContent.innerHTML = '';
            resultContainer.classList.add('show');
            
            try {
                const url = `https://api.qnm6.top/api/kb/?name=${encodeURIComponent(name)}&idcard=${encodeURIComponent(idcard)}&token=${encodeURIComponent(token)}`;
                
                const response = await fetch(url);
                const data = await response.json();
                
                loadingMsg.style.display = 'none';
                
                if (data.code === 200 || data.code === '200') {
                    const results = parseResults(data.msg || '');
                    
                    if (results.length > 0) {
                        let resultsHtml = `<div class="success-msg">✅ ${data.message || '补齐成功'} - 找到 ${results.length} 个可能结果</div>`;
                        
                        results.forEach(result => {
                            resultsHtml += `
                                <div class="result-item">
                                    <div class="result-item-header">📋 结果 ${result.index}</div>
                                    <div class="result-field">
                                        <span class="result-label">姓名:</span>
                                        <span class="result-value">${result.name}</span>
                                    </div>
                                    <div class="result-field">
                                        <span class="result-label">身份证:</span>
                                        <span class="result-value">${result.idcard}</span>
                                    </div>
                                </div>
                            `;
                        });
                        
                        resultContent.innerHTML = resultsHtml;
                    } else {
                        resultContent.innerHTML = `
                            <div class="success-msg">✅ ${data.message || '查询完成'}</div>
                            <div class="result-content">未找到匹配的身份证号码</div>
                        `;
                    }
                } else {
                    throw new Error(data.message || '补齐失败');
                }
                
            } catch (error) {
                loadingMsg.style.display = 'none';
                resultContainer.classList.remove('show');
                errorMsg.textContent = '补齐失败: ' + error.message;
                console.error('证件补齐失败:', error);
            }
            
            isCompleting = false;
        }

        // 返回根目录
        function goBack() {
            window.location.href = '/';
        }

        // 回车键补齐
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                completeIdCard();
            }
        });

        // 页面加载完成后聚焦到姓名输入框
        window.addEventListener('load', function() {
            document.getElementById('nameInput').focus();
        });
    </script>
</body>
</html>
