<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快手查询-猎户助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: url('https://www.loliapi.com/acg/pe/') center/cover no-repeat fixed;
            min-height: 100vh;
            padding: 16px;
            margin: 0;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.85);
            z-index: -1;
        }

        .top-bar {
            background: url('https://www.loliapi.com/acg/pc/') center/cover no-repeat;
            border: 3px solid #333;
            border-radius: 8px;
            padding: 20px 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 16px;
            box-shadow: 4px 4px 0px #333;
            min-height: 120px;
            position: relative;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            backdrop-filter: blur(2px);
        }

        .pixel-icons {
            display: flex;
            gap: 12px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .pixel-icon {
            width: 24px;
            height: 24px;
            background: #ff6b35;
            border: 2px solid #333;
            position: relative;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .pixel-icon.orange {
            background: #ff6b35;
        }

        .pixel-icon.gray {
            background: #999;
        }

        .pixel-icon.green {
            background: #4CAF50;
        }

        .pixel-icon.blue {
            background: #2196F3;
            position: relative;
            z-index: 2;
        }

        .pixel-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
        }

        .header-card {
            background: linear-gradient(135deg, #FF6F00 0%, #E65100 100%);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(5px);
        }

        .app-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 16px 0;
            text-shadow: 2px 2px 0px rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-title::before {
            content: '⚡';
            font-size: 16px;
        }

        .back-btn {
            background: rgba(0,0,0,0.3);
            color: white;
            border: 3px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.1s ease;
            text-shadow: 1px 1px 0px rgba(0,0,0,0.5);
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.5);
            transform: translate(1px, 1px);
            box-shadow: 1px 1px 0px rgba(0,0,0,0.3);
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
        }

        .input-group {
            margin-bottom: 16px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 6px;
            color: #333;
            font-weight: bold;
            font-size: 14px;
        }

        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 3px solid #333;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: all 0.1s ease;
            outline: none;
            background: #f9f9f9;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
        }

        input[type="text"]:focus, input[type="password"]:focus {
            background: white;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.2);
        }

        button {
            background: #FF6F00;
            color: white;
            border: 3px solid #333;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            cursor: pointer;
            transition: all 0.1s ease;
            margin: 6px 4px;
            box-shadow: 3px 3px 0px #333;
        }

        button:hover {
            transform: translate(1px, 1px);
            box-shadow: 2px 2px 0px #333;
        }

        button:active {
            transform: translate(2px, 2px);
            box-shadow: 1px 1px 0px #333;
        }

        .query-btn {
            width: 100%;
        }

        .result-container {
            margin-top: 20px;
            padding: 20px;
            background: rgba(249, 249, 249, 0.95);
            border: 3px solid #333;
            border-radius: 8px;
            display: none;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
            backdrop-filter: blur(5px);
        }

        .result-container.show {
            display: block;
        }

        .kuaishou-info {
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 12px 0;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.1);
            text-align: center;
        }

        .kuaishou-header {
            font-size: 16px;
            font-weight: bold;
            color: #FF6F00;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 2px solid #fff3e0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
            margin-top: 16px;
        }

        .info-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            transition: all 0.1s ease;
        }

        .info-item:hover {
            border-color: #FF6F00;
            transform: translateY(-1px);
            box-shadow: 2px 2px 0px rgba(0,0,0,0.1);
        }

        .info-label {
            font-weight: bold;
            color: #666;
            font-size: 13px;
            margin-bottom: 8px;
            display: block;
        }

        .info-value {
            color: #333;
            font-size: 15px;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            font-weight: bold;
        }

        .kuaishou-id {
            background: #fff3e0;
            border: 2px solid #FF6F00;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            text-align: center;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #e65100;
            font-weight: bold;
        }

        .loading {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            color: #666;
            animation: pulse 1.5s infinite;
            padding: 40px;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .error {
            color: #f44336;
            margin-top: 10px;
            font-size: 12px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            text-align: center;
        }

        .warning {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #856404;
            text-align: center;
        }

        .success-msg {
            color: #4CAF50;
            font-weight: bold;
            text-align: center;
            margin-bottom: 16px;
        }

        .feature-note {
            background: #fff3e0;
            border: 2px solid #FF6F00;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #e65100;
            text-align: center;
        }

        .example-box {
            background: #f5f5f5;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #666;
        }

        .example-box h4 {
            color: #FF6F00;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .example-box ul {
            margin-left: 16px;
            line-height: 1.5;
        }

        .example-box code {
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        @media (max-width: 480px) {
            .top-bar {
                margin: 0 8px 12px 8px;
                padding: 16px 12px;
                min-height: 100px;
            }
            
            .header-card {
                margin: 0 8px 12px 8px;
                padding: 16px;
            }
            
            .container {
                margin: 0 8px;
                padding: 16px;
            }
            
            .app-title {
                font-size: 16px;
            }
            
            h1 {
                font-size: 18px;
            }
            
            button {
                width: 100%;
                margin: 4px 0;
            }

            .kuaishou-header {
                flex-direction: column;
                gap: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="top-bar">
        <div class="pixel-icons">
            <div class="pixel-icon orange"></div>
            <div class="pixel-icon gray"></div>
            <div class="pixel-icon green"></div>
        </div>
        <div class="pixel-icon blue"></div>
    </div>
    
    <div class="header-card">
        <h2 class="app-title">快手查询-猎户助手</h2>
        <button class="back-btn" onclick="goBack()">返回首页</button>
    </div>
    
    <div class="container">
        <h1>⚡ 快手查询</h1>
        
        <div class="feature-note">
            🔍 快手身份查询 - 根据快手号查询对应的身份信息
        </div>
        
        <div class="example-box">
            <h4>📝 快手号格式示例：</h4>
            <ul>
                <li>快手ID格式：<code>kuaishouid_xxxxxxxxx</code></li>
                <li>数字快手号：<code>123456789</code></li>
                <li>自定义快手号：<code>abc123456</code></li>
                <li>手机号快手：<code>13812345678</code></li>
            </ul>
        </div>
        
        <div class="warning">
            ⚠️ 请确保查询用途合法，尊重他人隐私
        </div>
        
        <div class="input-group">
            <label for="kuaishouInput">快手号：</label>
            <input type="text" id="kuaishouInput" placeholder="请输入快手号或快手ID">
            <div class="error" id="kuaishouError"></div>
        </div>

        <div class="input-group">
            <label for="tokenInput">卡密(Token)：</label>
            <input type="password" id="tokenInput" placeholder="请输入您的Token">
            <div class="error" id="tokenError"></div>
        </div>

        <button class="query-btn" onclick="queryKuaishou()">🔍 查询快手</button>

        <div class="result-container" id="resultContainer">
            <div id="loadingMsg" class="loading" style="display: none;">正在查询快手信息...</div>
            <div id="resultContent"></div>
        </div>
        
        <div class="error" id="errorMsg"></div>
    </div>

    <script>
        let isQuerying = false;

        // 验证快手号格式
        function validateKuaishou(kuaishou) {
            // 快手号可以是多种格式：kuaishouid_xxx、数字号、自定义号、手机号等
            if (!kuaishou || kuaishou.length < 3) {
                return false;
            }
            
            // 基本长度和字符检查
            if (kuaishou.length > 50) {
                return false;
            }
            
            return true;
        }

        // 查询快手信息
        async function queryKuaishou() {
            if (isQuerying) return;
            
            const kuaishouInput = document.getElementById('kuaishouInput');
            const tokenInput = document.getElementById('tokenInput');
            const kuaishouError = document.getElementById('kuaishouError');
            const tokenError = document.getElementById('tokenError');
            const errorMsg = document.getElementById('errorMsg');
            const resultContainer = document.getElementById('resultContainer');
            const loadingMsg = document.getElementById('loadingMsg');
            const resultContent = document.getElementById('resultContent');
            
            // 清除之前的错误信息
            kuaishouError.textContent = '';
            tokenError.textContent = '';
            errorMsg.textContent = '';
            
            const kuaishou = kuaishouInput.value.trim();
            const token = tokenInput.value.trim();
            
            // 验证输入
            let hasError = false;
            
            if (!kuaishou) {
                kuaishouError.textContent = '请输入快手号';
                hasError = true;
            } else if (!validateKuaishou(kuaishou)) {
                kuaishouError.textContent = '请输入有效的快手号';
                hasError = true;
            }
            
            if (!token) {
                tokenError.textContent = '请输入Token';
                hasError = true;
            }
            
            if (hasError) {
                return;
            }
            
            isQuerying = true;
            
            // 显示加载状态
            loadingMsg.style.display = 'block';
            resultContent.innerHTML = '';
            resultContainer.classList.add('show');
            
            try {
                const url = `https://api.qnm6.top/api/dujia/index.php?action=generate_id&token=${encodeURIComponent(token)}&kuaishou=${encodeURIComponent(kuaishou)}`;
                
                const response = await fetch(url);
                const data = await response.json();
                
                loadingMsg.style.display = 'none';
                
                if (data.code === 200 || data.code === '200') {
                    const kuaishouData = data.data || {};
                    
                    resultContent.innerHTML = `
                        <div class="success-msg">✅ 查询成功</div>
                        
                        <div class="kuaishou-id">
                            ⚡ 查询快手号: ${kuaishou}
                        </div>
                        
                        <div class="kuaishou-info">
                            <div class="kuaishou-header">
                                👤 身份信息
                            </div>
                            
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-label">真实姓名:</span>
                                    <span class="info-value">${kuaishouData.name || '未知'}</span>
                                </div>
                                
                                <div class="info-item">
                                    <span class="info-label">身份证号:</span>
                                    <span class="info-value">${kuaishouData.idcard || '未知'}</span>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    throw new Error(data.message || '查询失败');
                }
                
            } catch (error) {
                loadingMsg.style.display = 'none';
                resultContainer.classList.remove('show');
                errorMsg.textContent = '查询失败: ' + error.message;
                console.error('快手查询失败:', error);
            }
            
            isQuerying = false;
        }

        // 返回根目录
        function goBack() {
            window.location.href = '/';
        }

        // 回车键查询
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                queryKuaishou();
            }
        });

        // 页面加载完成后聚焦到快手号输入框
        window.addEventListener('load', function() {
            document.getElementById('kuaishouInput').focus();
        });
    </script>
</body>
</html>
