<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>号码检测-猎户助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: url('https://www.loliapi.com/acg/pe/') center/cover no-repeat fixed;
            min-height: 100vh;
            padding: 16px;
            margin: 0;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.85);
            z-index: -1;
        }

        .top-bar {
            background: url('https://www.loliapi.com/acg/pc/') center/cover no-repeat;
            border: 3px solid #333;
            border-radius: 8px;
            padding: 20px 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 16px;
            box-shadow: 4px 4px 0px #333;
            min-height: 120px;
            position: relative;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            backdrop-filter: blur(2px);
        }

        .pixel-icons {
            display: flex;
            gap: 12px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .pixel-icon {
            width: 24px;
            height: 24px;
            background: #ff6b35;
            border: 2px solid #333;
            position: relative;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .pixel-icon.orange {
            background: #ff6b35;
        }

        .pixel-icon.gray {
            background: #999;
        }

        .pixel-icon.green {
            background: #4CAF50;
        }

        .pixel-icon.blue {
            background: #2196F3;
            position: relative;
            z-index: 2;
        }

        .pixel-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
        }

        .header-card {
            background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(5px);
        }

        .app-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 16px 0;
            text-shadow: 2px 2px 0px rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-title::before {
            content: '📞';
            font-size: 16px;
        }

        .back-btn {
            background: rgba(0,0,0,0.3);
            color: white;
            border: 3px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.1s ease;
            text-shadow: 1px 1px 0px rgba(0,0,0,0.5);
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.5);
            transform: translate(1px, 1px);
            box-shadow: 1px 1px 0px rgba(0,0,0,0.3);
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
        }

        .input-group {
            margin-bottom: 16px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 6px;
            color: #333;
            font-weight: bold;
            font-size: 14px;
        }

        input[type="tel"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 3px solid #333;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: all 0.1s ease;
            outline: none;
            background: #f9f9f9;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
        }

        input[type="tel"]:focus, input[type="password"]:focus {
            background: white;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.2);
        }

        button {
            background: #4CAF50;
            color: white;
            border: 3px solid #333;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            cursor: pointer;
            transition: all 0.1s ease;
            margin: 6px 4px;
            box-shadow: 3px 3px 0px #333;
        }

        button:hover {
            transform: translate(1px, 1px);
            box-shadow: 2px 2px 0px #333;
        }

        button:active {
            transform: translate(2px, 2px);
            box-shadow: 1px 1px 0px #333;
        }

        .detect-btn {
            width: 100%;
        }

        .result-container {
            margin-top: 20px;
            padding: 20px;
            background: rgba(249, 249, 249, 0.95);
            border: 3px solid #333;
            border-radius: 8px;
            display: none;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
            backdrop-filter: blur(5px);
        }

        .result-container.show {
            display: block;
        }

        .result-content {
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
            font-size: 14px;
            line-height: 1.8;
            white-space: pre-line;
            word-break: break-word;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.1);
            font-family: 'Courier New', monospace;
        }

        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 8px;
        }

        .status-active {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4CAF50;
        }

        .status-inactive {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }

        .status-unknown {
            background: #fff3e0;
            color: #ef6c00;
            border: 1px solid #ff9800;
        }

        .loading {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            color: #666;
            animation: pulse 1.5s infinite;
            padding: 40px;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .error {
            color: #f44336;
            margin-top: 10px;
            font-size: 12px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            text-align: center;
        }

        .success-msg {
            color: #4CAF50;
            font-weight: bold;
            text-align: center;
            margin-bottom: 16px;
        }

        .feature-note {
            background: #e8f5e8;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #2e7d32;
            text-align: center;
        }

        @media (max-width: 480px) {
            .top-bar {
                margin: 0 8px 12px 8px;
                padding: 16px 12px;
                min-height: 100px;
            }
            
            .header-card {
                margin: 0 8px 12px 8px;
                padding: 16px;
            }
            
            .container {
                margin: 0 8px;
                padding: 16px;
            }
            
            .app-title {
                font-size: 16px;
            }
            
            h1 {
                font-size: 18px;
            }
            
            button {
                width: 100%;
                margin: 4px 0;
            }
        }
    </style>
</head>
<body>
    <div class="top-bar">
        <div class="pixel-icons">
            <div class="pixel-icon orange"></div>
            <div class="pixel-icon gray"></div>
            <div class="pixel-icon green"></div>
        </div>
        <div class="pixel-icon blue"></div>
    </div>
    
    <div class="header-card">
        <h2 class="app-title">号码检测-猎户助手</h2>
        <button class="back-btn" onclick="goBack()">返回首页</button>
    </div>
    
    <div class="container">
        <h1>📞 号码检测</h1>
        
        <div class="feature-note">
            📱 手机号状态检测 - 检测号码是否为空号及开通时间
        </div>
        
        <div class="input-group">
            <label for="phoneInput">手机号码：</label>
            <input type="tel" id="phoneInput" placeholder="请输入11位手机号码" maxlength="11">
            <div class="error" id="phoneError"></div>
        </div>

        <div class="input-group">
            <label for="tokenInput">卡密(Token)：</label>
            <input type="password" id="tokenInput" placeholder="请输入您的Token">
            <div class="error" id="tokenError"></div>
        </div>

        <button class="detect-btn" onclick="detectPhone()">🔍 开始检测</button>

        <div class="result-container" id="resultContainer">
            <div id="loadingMsg" class="loading" style="display: none;">正在检测号码状态...</div>
            <div id="resultContent"></div>
        </div>
        
        <div class="error" id="errorMsg"></div>
    </div>

    <script>
        let isDetecting = false;

        // 验证手机号码格式
        function validatePhone(phone) {
            const phoneRegex = /^1[3-9]\d{9}$/;
            return phoneRegex.test(phone);
        }

        // 获取状态指示器
        function getStatusIndicator(result) {
            const resultLower = result.toLowerCase();
            if (resultLower.includes('空号') || resultLower.includes('停机') || resultLower.includes('注销')) {
                return '<span class="status-indicator status-inactive">空号/停机</span>';
            } else if (resultLower.includes('正常') || resultLower.includes('在网') || resultLower.includes('活跃')) {
                return '<span class="status-indicator status-active">正常在网</span>';
            } else {
                return '<span class="status-indicator status-unknown">状态未知</span>';
            }
        }

        // 格式化检测结果
        function formatResult(data) {
            const lines = data.split('\n');
            let formattedResult = '';
            
            lines.forEach(line => {
                if (line.includes('检测结果:')) {
                    const parts = line.split(':');
                    if (parts.length >= 2) {
                        const result = parts[1].trim();
                        formattedResult += `${parts[0]}:${result}${getStatusIndicator(result)}\n`;
                    } else {
                        formattedResult += line + '\n';
                    }
                } else {
                    formattedResult += line + '\n';
                }
            });
            
            return formattedResult.trim();
        }

        // 检测手机号
        async function detectPhone() {
            if (isDetecting) return;
            
            const phoneInput = document.getElementById('phoneInput');
            const tokenInput = document.getElementById('tokenInput');
            const phoneError = document.getElementById('phoneError');
            const tokenError = document.getElementById('tokenError');
            const errorMsg = document.getElementById('errorMsg');
            const resultContainer = document.getElementById('resultContainer');
            const loadingMsg = document.getElementById('loadingMsg');
            const resultContent = document.getElementById('resultContent');
            
            // 清除之前的错误信息
            phoneError.textContent = '';
            tokenError.textContent = '';
            errorMsg.textContent = '';
            
            const phone = phoneInput.value.trim();
            const token = tokenInput.value.trim();
            
            // 验证输入
            let hasError = false;
            
            if (!phone) {
                phoneError.textContent = '请输入手机号码';
                hasError = true;
            } else if (!validatePhone(phone)) {
                phoneError.textContent = '请输入正确的11位手机号码';
                hasError = true;
            }
            
            if (!token) {
                tokenError.textContent = '请输入Token';
                hasError = true;
            }
            
            if (hasError) {
                return;
            }
            
            isDetecting = true;
            
            // 显示加载状态
            loadingMsg.style.display = 'block';
            resultContent.innerHTML = '';
            resultContainer.classList.add('show');
            
            try {
                const url = `https://api.qnm6.top/api/khjc/?phone=${encodeURIComponent(phone)}&token=${encodeURIComponent(token)}`;
                
                const response = await fetch(url);
                const data = await response.json();
                
                loadingMsg.style.display = 'none';
                
                if (data.code === 200 || data.code === '200') {
                    const formattedData = formatResult(data.shuju || '暂无检测结果');
                    
                    resultContent.innerHTML = `
                        <div class="success-msg">✅ ${data.message || '检测成功'}</div>
                        <div class="result-content">${formattedData}</div>
                    `;
                } else {
                    throw new Error(data.message || '检测失败');
                }
                
            } catch (error) {
                loadingMsg.style.display = 'none';
                resultContainer.classList.remove('show');
                errorMsg.textContent = '检测失败: ' + error.message;
                console.error('号码检测失败:', error);
            }
            
            isDetecting = false;
        }

        // 返回根目录
        function goBack() {
            window.location.href = '/';
        }

        // 回车键检测
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                detectPhone();
            }
        });

        // 页面加载完成后聚焦到手机号输入框
        window.addEventListener('load', function() {
            document.getElementById('phoneInput').focus();
        });
    </script>
</body>
</html>
