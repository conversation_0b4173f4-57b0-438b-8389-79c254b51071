<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>婚姻查询-猎户助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: url('https://www.loliapi.com/acg/pe/') center/cover no-repeat fixed;
            min-height: 100vh;
            padding: 16px;
            margin: 0;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.85);
            z-index: -1;
        }

        .top-bar {
            background: url('https://www.loliapi.com/acg/pc/') center/cover no-repeat;
            border: 3px solid #333;
            border-radius: 8px;
            padding: 20px 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 16px;
            box-shadow: 4px 4px 0px #333;
            min-height: 120px;
            position: relative;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            backdrop-filter: blur(2px);
        }

        .pixel-icons {
            display: flex;
            gap: 12px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .pixel-icon {
            width: 24px;
            height: 24px;
            background: #ff6b35;
            border: 2px solid #333;
            position: relative;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .pixel-icon.orange {
            background: #ff6b35;
        }

        .pixel-icon.gray {
            background: #999;
        }

        .pixel-icon.green {
            background: #4CAF50;
        }

        .pixel-icon.blue {
            background: #2196F3;
            position: relative;
            z-index: 2;
        }

        .pixel-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
        }

        .header-card {
            background: linear-gradient(135deg, #E91E63 0%, #C2185B 100%);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(5px);
        }

        .app-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 16px 0;
            text-shadow: 2px 2px 0px rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-title::before {
            content: '💒';
            font-size: 16px;
        }

        .back-btn {
            background: rgba(0,0,0,0.3);
            color: white;
            border: 3px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.1s ease;
            text-shadow: 1px 1px 0px rgba(0,0,0,0.5);
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.5);
            transform: translate(1px, 1px);
            box-shadow: 1px 1px 0px rgba(0,0,0,0.3);
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
        }

        .input-group {
            margin-bottom: 16px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 6px;
            color: #333;
            font-weight: bold;
            font-size: 14px;
        }

        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 3px solid #333;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: all 0.1s ease;
            outline: none;
            background: #f9f9f9;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
        }

        input[type="text"]:focus, input[type="password"]:focus {
            background: white;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.2);
        }

        button {
            background: #E91E63;
            color: white;
            border: 3px solid #333;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            cursor: pointer;
            transition: all 0.1s ease;
            margin: 6px 4px;
            box-shadow: 3px 3px 0px #333;
        }

        button:hover {
            transform: translate(1px, 1px);
            box-shadow: 2px 2px 0px #333;
        }

        button:active {
            transform: translate(2px, 2px);
            box-shadow: 1px 1px 0px #333;
        }

        .query-btn {
            width: 100%;
        }

        .result-container {
            margin-top: 20px;
            padding: 20px;
            background: rgba(249, 249, 249, 0.95);
            border: 3px solid #333;
            border-radius: 8px;
            display: none;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
            backdrop-filter: blur(5px);
        }

        .result-container.show {
            display: block;
        }

        .marriage-summary {
            background: #fce4ec;
            border: 2px solid #E91E63;
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
            text-align: center;
        }

        .summary-title {
            font-size: 16px;
            font-weight: bold;
            color: #C2185B;
            margin-bottom: 12px;
        }

        .summary-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            font-size: 13px;
        }

        .summary-item {
            background: white;
            padding: 8px;
            border-radius: 6px;
            border: 1px solid #f8bbd9;
        }

        .summary-label {
            font-weight: bold;
            color: #666;
            display: block;
            margin-bottom: 4px;
        }

        .summary-value {
            color: #E91E63;
            font-weight: bold;
        }

        .marriage-history {
            margin-top: 16px;
        }

        .history-title {
            font-size: 14px;
            font-weight: bold;
            color: #E91E63;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .marriage-record {
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.1);
        }

        .record-header {
            font-size: 14px;
            font-weight: bold;
            color: #E91E63;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 2px solid #fce4ec;
        }

        .record-info {
            display: grid;
            grid-template-columns: 1fr;
            gap: 8px;
        }

        .record-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 6px 0;
            border-bottom: 1px solid #f5f5f5;
        }

        .record-item:last-child {
            border-bottom: none;
        }

        .record-label {
            font-weight: bold;
            color: #666;
            font-size: 13px;
            min-width: 80px;
        }

        .record-value {
            color: #333;
            font-size: 13px;
            text-align: right;
            flex: 1;
        }

        .loading {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            color: #666;
            animation: pulse 1.5s infinite;
            padding: 40px;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .error {
            color: #f44336;
            margin-top: 10px;
            font-size: 12px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            text-align: center;
        }

        .warning {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #856404;
            text-align: center;
        }

        .success-msg {
            color: #4CAF50;
            font-weight: bold;
            text-align: center;
            margin-bottom: 16px;
        }

        .feature-note {
            background: #fce4ec;
            border: 2px solid #E91E63;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #c2185b;
            text-align: center;
        }

        @media (max-width: 480px) {
            .top-bar {
                margin: 0 8px 12px 8px;
                padding: 16px 12px;
                min-height: 100px;
            }
            
            .header-card {
                margin: 0 8px 12px 8px;
                padding: 16px;
            }
            
            .container {
                margin: 0 8px;
                padding: 16px;
            }
            
            .app-title {
                font-size: 16px;
            }
            
            h1 {
                font-size: 18px;
            }
            
            button {
                width: 100%;
                margin: 4px 0;
            }

            .summary-info {
                grid-template-columns: 1fr;
            }

            .record-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .record-value {
                text-align: left;
            }
        }
    </style>
</head>
<body>
    <div class="top-bar">
        <div class="pixel-icons">
            <div class="pixel-icon orange"></div>
            <div class="pixel-icon gray"></div>
            <div class="pixel-icon green"></div>
        </div>
        <div class="pixel-icon blue"></div>
    </div>
    
    <div class="header-card">
        <h2 class="app-title">婚姻查询-猎户助手</h2>
        <button class="back-btn" onclick="goBack()">返回首页</button>
    </div>
    
    <div class="container">
        <h1>💒 婚姻查询</h1>
        
        <div class="feature-note">
            💕 婚姻状况查询 - 查询个人的婚姻历史和配偶信息
        </div>
        
        <div class="warning">
            ⚠️ 请确保查询用途合法，尊重他人隐私
        </div>
        
        <div class="input-group">
            <label for="nameInput">姓名：</label>
            <input type="text" id="nameInput" placeholder="请输入真实姓名">
            <div class="error" id="nameError"></div>
        </div>

        <div class="input-group">
            <label for="idcardInput">身份证号码：</label>
            <input type="text" id="idcardInput" placeholder="请输入18位身份证号码" maxlength="18">
            <div class="error" id="idcardError"></div>
        </div>

        <div class="input-group">
            <label for="tokenInput">卡密(Token)：</label>
            <input type="password" id="tokenInput" placeholder="请输入您的Token">
            <div class="error" id="tokenError"></div>
        </div>

        <button class="query-btn" onclick="queryMarriage()">💕 查询婚姻</button>

        <div class="result-container" id="resultContainer">
            <div id="loadingMsg" class="loading" style="display: none;">正在查询婚姻信息...</div>
            <div id="resultContent"></div>
        </div>
        
        <div class="error" id="errorMsg"></div>
    </div>

    <script>
        let isQuerying = false;

        // 验证身份证号码格式
        function validateIdCard(idCard) {
            const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
            return idCardRegex.test(idCard);
        }

        // 验证姓名格式
        function validateName(name) {
            const nameRegex = /^[\u4e00-\u9fa5]{2,10}$/;
            return nameRegex.test(name);
        }

        // 查询婚姻信息
        async function queryMarriage() {
            if (isQuerying) return;
            
            const nameInput = document.getElementById('nameInput');
            const idcardInput = document.getElementById('idcardInput');
            const tokenInput = document.getElementById('tokenInput');
            const nameError = document.getElementById('nameError');
            const idcardError = document.getElementById('idcardError');
            const tokenError = document.getElementById('tokenError');
            const errorMsg = document.getElementById('errorMsg');
            const resultContainer = document.getElementById('resultContainer');
            const loadingMsg = document.getElementById('loadingMsg');
            const resultContent = document.getElementById('resultContent');
            
            // 清除之前的错误信息
            nameError.textContent = '';
            idcardError.textContent = '';
            tokenError.textContent = '';
            errorMsg.textContent = '';
            
            const name = nameInput.value.trim();
            const idcard = idcardInput.value.trim();
            const token = tokenInput.value.trim();
            
            // 验证输入
            let hasError = false;
            
            if (!name) {
                nameError.textContent = '请输入姓名';
                hasError = true;
            } else if (!validateName(name)) {
                nameError.textContent = '请输入正确的中文姓名(2-10个字符)';
                hasError = true;
            }
            
            if (!idcard) {
                idcardError.textContent = '请输入身份证号码';
                hasError = true;
            } else if (!validateIdCard(idcard)) {
                idcardError.textContent = '请输入正确的18位身份证号码';
                hasError = true;
            }
            
            if (!token) {
                tokenError.textContent = '请输入Token';
                hasError = true;
            }
            
            if (hasError) {
                return;
            }
            
            isQuerying = true;
            
            // 显示加载状态
            loadingMsg.style.display = 'block';
            resultContent.innerHTML = '';
            resultContainer.classList.add('show');
            
            try {
                const url = `https://api.qnm6.top/api/dujia/index.php?action=generate_marriage&token=${encodeURIComponent(token)}&name=${encodeURIComponent(name)}&idcard=${encodeURIComponent(idcard)}`;
                
                const response = await fetch(url);
                const data = await response.json();
                
                loadingMsg.style.display = 'none';
                
                if (data.code === 200 || data.code === '200') {
                    const marriageData = data.data || {};
                    const marriageHistory = marriageData['婚姻史'] || [];
                    
                    let historyHtml = '';
                    if (marriageHistory.length > 0) {
                        marriageHistory.forEach((record, index) => {
                            historyHtml += `
                                <div class="marriage-record">
                                    <div class="record-header">📋 婚姻记录 ${index + 1}</div>
                                    <div class="record-info">
                                        <div class="record-item">
                                            <span class="record-label">配偶姓名:</span>
                                            <span class="record-value">${record['配偶姓名'] || '未知'}</span>
                                        </div>
                                        <div class="record-item">
                                            <span class="record-label">登记时间:</span>
                                            <span class="record-value">${record['登记时间'] || '未知'}</span>
                                        </div>
                                        <div class="record-item">
                                            <span class="record-label">离婚时间:</span>
                                            <span class="record-value">${record['离婚时间'] || '未离婚'}</span>
                                        </div>
                                        <div class="record-item">
                                            <span class="record-label">离婚原因:</span>
                                            <span class="record-value">${record['离婚原因'] || '无'}</span>
                                        </div>
                                        <div class="record-item">
                                            <span class="record-label">子女情况:</span>
                                            <span class="record-value">${record['子女情况'] || '无'}</span>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                    }
                    
                    resultContent.innerHTML = `
                        <div class="success-msg">✅ 查询成功</div>
                        
                        <div class="marriage-summary">
                            <div class="summary-title">💒 婚姻状况概览</div>
                            <div class="summary-info">
                                <div class="summary-item">
                                    <span class="summary-label">姓名</span>
                                    <span class="summary-value">${marriageData['姓名'] || name}</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">身份证号</span>
                                    <span class="summary-value">${marriageData['身份证号'] || idcard}</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">婚姻状况</span>
                                    <span class="summary-value">${marriageData['婚姻状况'] || '未知'}</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">婚姻次数</span>
                                    <span class="summary-value">${marriageData['婚姻次数'] || 0} 次</span>
                                </div>
                            </div>
                        </div>
                        
                        ${marriageHistory.length > 0 ? `
                            <div class="marriage-history">
                                <div class="history-title">📚 婚姻历史记录</div>
                                ${historyHtml}
                            </div>
                        ` : `
                            <div class="marriage-history">
                                <div class="history-title">📚 婚姻历史记录</div>
                                <div class="marriage-record">
                                    <div style="text-align: center; color: #666; padding: 20px;">
                                        暂无婚姻历史记录
                                    </div>
                                </div>
                            </div>
                        `}
                    `;
                } else {
                    throw new Error(data.message || '查询失败');
                }
                
            } catch (error) {
                loadingMsg.style.display = 'none';
                resultContainer.classList.remove('show');
                errorMsg.textContent = '查询失败: ' + error.message;
                console.error('婚姻查询失败:', error);
            }
            
            isQuerying = false;
        }

        // 返回根目录
        function goBack() {
            window.location.href = '/';
        }

        // 回车键查询
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                queryMarriage();
            }
        });

        // 页面加载完成后聚焦到姓名输入框
        window.addEventListener('load', function() {
            document.getElementById('nameInput').focus();
        });
    </script>
</body>
</html>
