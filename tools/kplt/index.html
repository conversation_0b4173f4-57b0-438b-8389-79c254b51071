<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡泡聆听-猎户助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: url('https://www.loliapi.com/acg/pe/') center/cover no-repeat fixed;
            min-height: 100vh;
            padding: 16px;
            margin: 0;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.85);
            z-index: -1;
        }

        .top-bar {
            background: url('https://www.loliapi.com/acg/pc/') center/cover no-repeat;
            border: 3px solid #333;
            border-radius: 8px;
            padding: 20px 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 16px;
            box-shadow: 4px 4px 0px #333;
            min-height: 120px;
            position: relative;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            backdrop-filter: blur(2px);
        }

        .pixel-icons {
            display: flex;
            gap: 12px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .pixel-icon {
            width: 24px;
            height: 24px;
            background: #ff6b35;
            border: 2px solid #333;
            position: relative;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .pixel-icon.orange {
            background: #ff6b35;
        }

        .pixel-icon.gray {
            background: #999;
        }

        .pixel-icon.green {
            background: #4CAF50;
        }

        .pixel-icon.blue {
            background: #2196F3;
            position: relative;
            z-index: 2;
        }

        .pixel-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
        }

        .header-card {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(5px);
        }

        .app-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 16px 0;
            text-shadow: 2px 2px 0px rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-title::before {
            content: '🎧';
            font-size: 16px;
        }

        .back-btn {
            background: rgba(0,0,0,0.3);
            color: white;
            border: 3px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.1s ease;
            text-shadow: 1px 1px 0px rgba(0,0,0,0.5);
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.5);
            transform: translate(1px, 1px);
            box-shadow: 1px 1px 0px rgba(0,0,0,0.3);
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
        }

        .input-group {
            margin-bottom: 16px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 6px;
            color: #333;
            font-weight: bold;
            font-size: 14px;
        }

        input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 3px solid #333;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: all 0.1s ease;
            outline: none;
            background: #f9f9f9;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
        }

        input[type="password"]:focus {
            background: white;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.2);
        }

        button {
            background: #FF9800;
            color: white;
            border: 3px solid #333;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            cursor: pointer;
            transition: all 0.1s ease;
            margin: 6px 4px;
            box-shadow: 3px 3px 0px #333;
        }

        button:hover {
            transform: translate(1px, 1px);
            box-shadow: 2px 2px 0px #333;
        }

        button:active {
            transform: translate(2px, 2px);
            box-shadow: 1px 1px 0px #333;
        }

        .get-btn {
            width: 100%;
        }

        .result-container {
            margin-top: 20px;
            padding: 20px;
            background: rgba(249, 249, 249, 0.95);
            border: 3px solid #333;
            border-radius: 8px;
            display: none;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
            backdrop-filter: blur(5px);
            text-align: center;
        }

        .result-container.show {
            display: block;
        }

        .audio-player {
            width: 100%;
            margin: 16px 0;
            border: 3px solid #333;
            border-radius: 8px;
            background: #333;
            box-shadow: 4px 4px 0px rgba(0,0,0,0.3);
        }

        .audio-player::-webkit-media-controls-panel {
            background-color: #333;
        }

        .audio-info {
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            font-size: 12px;
            word-break: break-all;
        }

        .audio-info-label {
            font-weight: bold;
            color: #FF9800;
            margin-right: 8px;
        }

        .loading {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            color: #666;
            animation: pulse 1.5s infinite;
            padding: 40px;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .error {
            color: #f44336;
            margin-top: 10px;
            font-size: 12px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            text-align: center;
        }

        .success-msg {
            color: #4CAF50;
            font-weight: bold;
            text-align: center;
            margin-bottom: 16px;
        }

        .feature-note {
            background: #fff3e0;
            border: 2px solid #FF9800;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #f57c00;
            text-align: center;
        }

        .refresh-btn {
            background: #4CAF50;
            margin-top: 12px;
        }

        @media (max-width: 480px) {
            .top-bar {
                margin: 0 8px 12px 8px;
                padding: 16px 12px;
                min-height: 100px;
            }
            
            .header-card {
                margin: 0 8px 12px 8px;
                padding: 16px;
            }
            
            .container {
                margin: 0 8px;
                padding: 16px;
            }
            
            .app-title {
                font-size: 16px;
            }
            
            h1 {
                font-size: 18px;
            }
            
            button {
                width: 100%;
                margin: 4px 0;
            }
        }
    </style>
</head>
<body>
    <div class="top-bar">
        <div class="pixel-icons">
            <div class="pixel-icon orange"></div>
            <div class="pixel-icon gray"></div>
            <div class="pixel-icon green"></div>
        </div>
        <div class="pixel-icon blue"></div>
    </div>
    
    <div class="header-card">
        <h2 class="app-title">卡泡聆听-猎户助手</h2>
        <button class="back-btn" onclick="goBack()">返回首页</button>
    </div>
    
    <div class="container">
        <h1>🎧 卡泡聆听</h1>
        
        <div class="feature-note">
            🎵 音频获取与播放 - 获取随机音频内容进行在线聆听
        </div>
        
        <div class="input-group">
            <label for="tokenInput">卡密(Token)：</label>
            <input type="password" id="tokenInput" placeholder="请输入您的Token">
            <div class="error" id="tokenError"></div>
        </div>

        <button class="get-btn" onclick="getAudio()">🎵 获取音频</button>

        <div class="result-container" id="resultContainer">
            <div id="loadingMsg" class="loading" style="display: none;">正在获取音频...</div>
            <div id="resultContent"></div>
        </div>
        
        <div class="error" id="errorMsg"></div>
    </div>

    <script>
        let isGetting = false;
        let currentAudio = null;

        // 获取音频
        async function getAudio() {
            if (isGetting) return;
            
            const tokenInput = document.getElementById('tokenInput');
            const tokenError = document.getElementById('tokenError');
            const errorMsg = document.getElementById('errorMsg');
            const resultContainer = document.getElementById('resultContainer');
            const loadingMsg = document.getElementById('loadingMsg');
            const resultContent = document.getElementById('resultContent');
            
            // 清除之前的错误信息
            tokenError.textContent = '';
            errorMsg.textContent = '';
            
            const token = tokenInput.value.trim();
            
            if (!token) {
                tokenError.textContent = '请输入Token';
                return;
            }
            
            isGetting = true;
            
            // 停止当前播放的音频
            if (currentAudio) {
                currentAudio.pause();
                currentAudio = null;
            }
            
            // 显示加载状态
            loadingMsg.style.display = 'block';
            resultContent.innerHTML = '';
            resultContainer.classList.add('show');
            
            try {
                const url = `https://api.qnm6.top/api/kp/?token=${encodeURIComponent(token)}`;
                
                const response = await fetch(url);
                const data = await response.json();
                
                loadingMsg.style.display = 'none';
                
                if (data.code === '200' || data.code === 200) {
                    if (data.mp3url) {
                        resultContent.innerHTML = `
                            <div class="success-msg">✅ ${data.message || '音频获取成功'}</div>
                            <audio class="audio-player" controls preload="metadata" id="audioPlayer">
                                <source src="${data.mp3url}" type="audio/mpeg">
                                您的浏览器不支持音频播放
                            </audio>
                            <div class="audio-info">
                                <span class="audio-info-label">音频链接:</span>${data.mp3url}
                            </div>
                            <button class="refresh-btn" onclick="getAudio()">🔄 获取新音频</button>
                        `;
                        
                        // 获取新的音频元素引用
                        currentAudio = document.getElementById('audioPlayer');
                        
                        // 添加音频事件监听
                        if (currentAudio) {
                            currentAudio.addEventListener('error', function() {
                                errorMsg.textContent = '音频加载失败，请重试';
                            });
                            
                            currentAudio.addEventListener('loadstart', function() {
                                console.log('开始加载音频');
                            });
                            
                            currentAudio.addEventListener('canplay', function() {
                                console.log('音频可以播放');
                            });
                        }
                    } else {
                        resultContent.innerHTML = `
                            <div class="success-msg">✅ ${data.message || '获取完成，但未返回音频'}</div>
                            <button class="refresh-btn" onclick="getAudio()">🔄 重新获取</button>
                        `;
                    }
                } else {
                    throw new Error(data.message || '音频获取失败');
                }
                
            } catch (error) {
                loadingMsg.style.display = 'none';
                resultContainer.classList.remove('show');
                errorMsg.textContent = '获取失败: ' + error.message;
                console.error('获取音频失败:', error);
            }
            
            isGetting = false;
        }

        // 返回根目录
        function goBack() {
            // 停止音频播放
            if (currentAudio) {
                currentAudio.pause();
                currentAudio = null;
            }
            window.location.href = '/';
        }

        // 回车键获取
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                getAudio();
            }
        });

        // 页面加载完成后聚焦到Token输入框
        window.addEventListener('load', function() {
            document.getElementById('tokenInput').focus();
        });

        // 页面卸载时停止音频
        window.addEventListener('beforeunload', function() {
            if (currentAudio) {
                currentAudio.pause();
            }
        });
    </script>
</body>
</html>
