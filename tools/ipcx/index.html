<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP查询-猎户助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: url('https://www.loliapi.com/acg/pe/') center/cover no-repeat fixed;
            min-height: 100vh;
            padding: 16px;
            margin: 0;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.85);
            z-index: -1;
        }

        .top-bar {
            background: url('https://www.loliapi.com/acg/pc/') center/cover no-repeat;
            border: 3px solid #333;
            border-radius: 8px;
            padding: 20px 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 16px;
            box-shadow: 4px 4px 0px #333;
            min-height: 120px;
            position: relative;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            backdrop-filter: blur(2px);
        }

        .pixel-icons {
            display: flex;
            gap: 12px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .pixel-icon {
            width: 24px;
            height: 24px;
            background: #ff6b35;
            border: 2px solid #333;
            position: relative;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .pixel-icon.orange {
            background: #ff6b35;
        }

        .pixel-icon.gray {
            background: #999;
        }

        .pixel-icon.green {
            background: #4CAF50;
        }

        .pixel-icon.blue {
            background: #2196F3;
            position: relative;
            z-index: 2;
        }

        .pixel-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
        }

        .header-card {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(5px);
        }

        .app-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 16px 0;
            text-shadow: 2px 2px 0px rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-title::before {
            content: '🌐';
            font-size: 16px;
        }

        .back-btn {
            background: rgba(0,0,0,0.3);
            color: white;
            border: 3px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.1s ease;
            text-shadow: 1px 1px 0px rgba(0,0,0,0.5);
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.5);
            transform: translate(1px, 1px);
            box-shadow: 1px 1px 0px rgba(0,0,0,0.3);
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
        }

        .input-group {
            margin-bottom: 16px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 6px;
            color: #333;
            font-weight: bold;
            font-size: 14px;
        }

        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 3px solid #333;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: all 0.1s ease;
            outline: none;
            background: #f9f9f9;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
        }

        input[type="text"]:focus {
            background: white;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.2);
        }

        button {
            background: #2196F3;
            color: white;
            border: 3px solid #333;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            cursor: pointer;
            transition: all 0.1s ease;
            margin: 6px 4px;
            box-shadow: 3px 3px 0px #333;
        }

        button:hover {
            transform: translate(1px, 1px);
            box-shadow: 2px 2px 0px #333;
        }

        button:active {
            transform: translate(2px, 2px);
            box-shadow: 1px 1px 0px #333;
        }

        .query-btn {
            width: 100%;
        }

        .my-ip-btn {
            background: #4CAF50;
        }

        .ip-result {
            margin-top: 20px;
            padding: 20px;
            background: rgba(249, 249, 249, 0.95);
            border: 3px solid #333;
            border-radius: 8px;
            display: none;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
            backdrop-filter: blur(5px);
        }

        .ip-result.show {
            display: block;
        }

        .result-item {
            margin-bottom: 12px;
            padding: 8px;
            background: white;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
        }

        .result-label {
            font-weight: bold;
            color: #2196F3;
            margin-right: 8px;
        }

        .loading {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            color: #666;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .error {
            color: #f44336;
            margin-top: 10px;
            font-size: 12px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            text-align: center;
        }

        @media (max-width: 480px) {
            .top-bar {
                margin: 0 8px 12px 8px;
                padding: 16px 12px;
                min-height: 100px;
            }
            
            .header-card {
                margin: 0 8px 12px 8px;
                padding: 16px;
            }
            
            .container {
                margin: 0 8px;
                padding: 16px;
            }
            
            .app-title {
                font-size: 16px;
            }
            
            h1 {
                font-size: 18px;
            }
            
            button {
                width: 100%;
                margin: 4px 0;
            }
        }
    </style>
</head>
<body>
    <div class="top-bar">
        <div class="pixel-icons">
            <div class="pixel-icon orange"></div>
            <div class="pixel-icon gray"></div>
            <div class="pixel-icon green"></div>
        </div>
        <div class="pixel-icon blue"></div>
    </div>
    
    <div class="header-card">
        <h2 class="app-title">IP查询-猎户助手</h2>
        <button class="back-btn" onclick="goBack()">返回首页</button>
    </div>
    
    <div class="container">
        <h1>🌐 IP地址查询</h1>
        
        <div class="input-group">
            <label for="ipInput">输入IP地址：</label>
            <input type="text" id="ipInput" placeholder="例如：**************">
            <div class="error" id="inputError"></div>
        </div>

        <button class="query-btn" onclick="queryIP()">🔍 查询IP信息</button>
        <button class="my-ip-btn" onclick="queryMyIP()">📍 查询我的IP</button>

        <div class="ip-result" id="ipResult">
            <div id="loadingMsg" class="loading" style="display: none;">正在查询中...</div>
            <div id="resultContent"></div>
        </div>
        
        <div class="error" id="errorMsg"></div>
    </div>

    <script>
        let isQuerying = false;

        // 验证IP地址格式
        function isValidIP(ip) {
            const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
            return ipRegex.test(ip);
        }

        // 查询IP信息
        async function queryIPInfo(ip) {
            if (isQuerying) return;
            
            isQuerying = true;
            const loadingMsg = document.getElementById('loadingMsg');
            const resultContent = document.getElementById('resultContent');
            const errorMsg = document.getElementById('errorMsg');
            const ipResult = document.getElementById('ipResult');
            
            // 显示加载状态
            loadingMsg.style.display = 'block';
            resultContent.innerHTML = '';
            errorMsg.textContent = '';
            ipResult.classList.add('show');
            
            try {
                const response = await fetch(`https://ipapi.co/${ip}/json/`);
                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.reason || '查询失败');
                }
                
                // 显示结果
                resultContent.innerHTML = `
                    <div class="result-item">
                        <span class="result-label">IP地址:</span>${data.ip || '未知'}
                    </div>
                    <div class="result-item">
                        <span class="result-label">国家:</span>${data.country_name || '未知'} (${data.country_code || ''})
                    </div>
                    <div class="result-item">
                        <span class="result-label">地区:</span>${data.region || '未知'}
                    </div>
                    <div class="result-item">
                        <span class="result-label">城市:</span>${data.city || '未知'}
                    </div>
                    <div class="result-item">
                        <span class="result-label">邮编:</span>${data.postal || '未知'}
                    </div>
                    <div class="result-item">
                        <span class="result-label">时区:</span>${data.timezone || '未知'}
                    </div>
                    <div class="result-item">
                        <span class="result-label">ISP:</span>${data.org || '未知'}
                    </div>
                    <div class="result-item">
                        <span class="result-label">坐标:</span>${data.latitude || '未知'}, ${data.longitude || '未知'}
                    </div>
                `;
                
                loadingMsg.style.display = 'none';
                
            } catch (error) {
                loadingMsg.style.display = 'none';
                errorMsg.textContent = '查询失败: ' + error.message;
                ipResult.classList.remove('show');
            }
            
            isQuerying = false;
        }

        // 查询指定IP
        function queryIP() {
            const ipInput = document.getElementById('ipInput');
            const inputError = document.getElementById('inputError');
            const ip = ipInput.value.trim();
            
            inputError.textContent = '';
            
            if (!ip) {
                inputError.textContent = '请输入IP地址';
                return;
            }
            
            if (!isValidIP(ip)) {
                inputError.textContent = 'IP地址格式不正确';
                return;
            }
            
            queryIPInfo(ip);
        }

        // 查询我的IP
        async function queryMyIP() {
            try {
                const response = await fetch('https://ipapi.co/json/');
                const data = await response.json();
                
                if (data.ip) {
                    document.getElementById('ipInput').value = data.ip;
                    queryIPInfo(data.ip);
                } else {
                    document.getElementById('errorMsg').textContent = '无法获取您的IP地址';
                }
            } catch (error) {
                document.getElementById('errorMsg').textContent = '获取IP地址失败';
            }
        }

        // 返回根目录
        function goBack() {
            window.location.href = '/';
        }

        // 回车键查询
        document.getElementById('ipInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                queryIP();
            }
        });

        // 页面加载完成后聚焦到输入框
        window.addEventListener('load', function() {
            document.getElementById('ipInput').focus();
        });
    </script>
</body>
</html>
